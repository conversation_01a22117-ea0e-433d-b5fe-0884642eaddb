name: budget_app_pk
description: A comprehensive budget management app designed for Pakistani users
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  
  # State Management
  flutter_riverpod: ^2.4.9
  
  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # UI Components
  cupertino_icons: ^1.0.6
  
  # Charts and Graphs
  fl_chart: ^1.0.0
  
  # Date and Time
  intl: ^0.20.2
  
  # Utilities
  uuid: ^4.2.1
  path_provider: ^2.1.1
  shared_preferences: ^2.2.2
  
  # Icons
  phosphor_flutter: ^2.0.1
  
  # Animations
  lottie: ^3.3.1
  
  # Image handling
  image_picker: ^1.0.4
  
  # File handling
  file_picker: ^10.2.0
  
  # Notifications
  flutter_local_notifications: ^19.2.1
  
  # Currency formatting
  money2: ^6.0.2

  # Timezone support
  timezone: ^0.10.0

  # Authentication & Backend
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  cloud_firestore: ^5.4.3
  firebase_storage: ^12.3.2

  # Animations & UI
  animate_do: ^3.3.4
  flutter_animate: ^4.5.0
  glassmorphism: ^3.0.0
  shimmer: ^3.0.0

  # Navigation & Routing
  go_router: ^14.6.1

  # Form validation
  form_validator: ^2.1.1

  # Biometric authentication
  local_auth: ^2.3.0

  # Secure storage
  flutter_secure_storage: ^9.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true
  generate: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    

