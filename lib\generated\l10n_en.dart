// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class SEn extends S {
  SEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Budget Manager PK';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get transactions => 'Transactions';

  @override
  String get budgets => 'Budgets';

  @override
  String get goals => 'Goals';

  @override
  String get settings => 'Settings';

  @override
  String get totalBalance => 'Total Balance';

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get addIncome => 'Add Income';

  @override
  String get addExpense => 'Add Expense';

  @override
  String get setBudget => 'Set Budget';

  @override
  String get addGoal => 'Add Goal';

  @override
  String get recentTransactions => 'Recent Transactions';

  @override
  String get spendingOverview => 'Spending Overview';

  @override
  String get budgetOverview => 'Budget Overview';

  @override
  String get viewAll => 'View All';

  @override
  String get noTransactionsYet => 'No transactions yet';

  @override
  String get startByAddingTransaction =>
      'Start by adding your first transaction';

  @override
  String get noBudgetsSet => 'No budgets set';

  @override
  String get createFirstBudget => 'Create your first budget to track spending';

  @override
  String get income => 'Income';

  @override
  String get expense => 'Expense';

  @override
  String get transfer => 'Transfer';

  @override
  String get amount => 'Amount';

  @override
  String get description => 'Description';

  @override
  String get category => 'Category';

  @override
  String get account => 'Account';

  @override
  String get date => 'Date';

  @override
  String get notes => 'Notes';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get cash => 'Cash';

  @override
  String get bank => 'Bank';

  @override
  String get mobile => 'Mobile';

  @override
  String get creditCard => 'Credit Card';

  @override
  String get today => 'Today';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get thisWeek => 'This Week';

  @override
  String get thisMonth => 'This Month';

  @override
  String get thisYear => 'This Year';

  @override
  String get weekly => 'Weekly';

  @override
  String get monthly => 'Monthly';

  @override
  String get quarterly => 'Quarterly';

  @override
  String get yearly => 'Yearly';

  @override
  String get active => 'Active';

  @override
  String get completed => 'Completed';

  @override
  String get paused => 'Paused';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get exceeded => 'Exceeded';

  @override
  String get nearLimit => 'Near Limit';

  @override
  String get onTrack => 'On Track';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get language => 'Language';

  @override
  String get currency => 'Currency';

  @override
  String get notifications => 'Notifications';

  @override
  String get backup => 'Backup';

  @override
  String get export => 'Export';

  @override
  String get import => 'Import';

  @override
  String get about => 'About';

  @override
  String get version => 'Version';

  @override
  String get goodMorning => 'Good Morning';

  @override
  String get goodAfternoon => 'Good Afternoon';

  @override
  String get goodEvening => 'Good Evening';

  @override
  String get financialTip => 'Financial Tip';
}
