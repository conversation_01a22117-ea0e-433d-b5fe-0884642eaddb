# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\Softwares\\Flutter SDK\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\Freelancing\\Flutter Apps\\Budget App" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\Softwares\\Flutter SDK\\flutter"
  "PROJECT_DIR=D:\\Freelancing\\Flutter Apps\\Budget App"
  "FLUTTER_ROOT=D:\\Softwares\\Flutter SDK\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\Freelancing\\Flutter Apps\\Budget App\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\Freelancing\\Flutter Apps\\Budget App"
  "FLUTTER_TARGET=D:\\Freelancing\\Flutter Apps\\Budget App\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNA==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049NmZiYTI0NDdlOQ==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049OGNkMTllNTA5ZA==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\Freelancing\\Flutter Apps\\Budget App\\.dart_tool\\package_config.json"
)
