// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for the en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "account": MessageLookupByLibrary.simpleMessage("Account"),
        "addExpense": MessageLookupByLibrary.simpleMessage("Add Expense"),
        "addGoal": MessageLookupByLibrary.simpleMessage("Add Goal"),
        "addIncome": MessageLookupByLibrary.simpleMessage("Add Income"),
        "amount": MessageLookupByLibrary.simpleMessage("Amount"),
        "appName": MessageLookupByLibrary.simpleMessage("Budget Manager PK"),
        "budgets": MessageLookupByLibrary.simpleMessage("Budgets"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "category": MessageLookupByLibrary.simpleMessage("Category"),
        "dashboard": MessageLookupByLibrary.simpleMessage("Dashboard"),
        "description": MessageLookupByLibrary.simpleMessage("Description"),
        "expense": MessageLookupByLibrary.simpleMessage("Expense"),
        "goals": MessageLookupByLibrary.simpleMessage("Goals"),
        "income": MessageLookupByLibrary.simpleMessage("Income"),
        "noTransactionsYet": MessageLookupByLibrary.simpleMessage("No transactions yet"),
        "quickActions": MessageLookupByLibrary.simpleMessage("Quick Actions"),
        "recentTransactions": MessageLookupByLibrary.simpleMessage("Recent Transactions"),
        "save": MessageLookupByLibrary.simpleMessage("Save"),
        "setBudget": MessageLookupByLibrary.simpleMessage("Set Budget"),
        "settings": MessageLookupByLibrary.simpleMessage("Settings"),
        "startByAddingTransaction": MessageLookupByLibrary.simpleMessage("Start by adding your first transaction"),
        "totalBalance": MessageLookupByLibrary.simpleMessage("Total Balance"),
        "transactions": MessageLookupByLibrary.simpleMessage("Transactions"),
        "transfer": MessageLookupByLibrary.simpleMessage("Transfer"),
        "viewAll": MessageLookupByLibrary.simpleMessage("View All"),
      };
}
