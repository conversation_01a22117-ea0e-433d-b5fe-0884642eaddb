// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for the ur locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ur';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "account": MessageLookupByLibrary.simpleMessage("اکاؤنٹ"),
        "addExpense": MessageLookupByLibrary.simpleMessage("خرچ شامل کریں"),
        "addGoal": MessageLookupByLibrary.simpleMessage("ہدف شامل کریں"),
        "addIncome": MessageLookupByLibrary.simpleMessage("آمدنی شامل کریں"),
        "amount": MessageLookupByLibrary.simpleMessage("رقم"),
        "appName": MessageLookupByLibrary.simpleMessage("بجٹ منیجر پاکستان"),
        "budgets": MessageLookupByLibrary.simpleMessage("بجٹ"),
        "cancel": MessageLookupByLibrary.simpleMessage("منسوخ"),
        "category": MessageLookupByLibrary.simpleMessage("قسم"),
        "dashboard": MessageLookupByLibrary.simpleMessage("ڈیش بورڈ"),
        "description": MessageLookupByLibrary.simpleMessage("تفصیل"),
        "expense": MessageLookupByLibrary.simpleMessage("خرچ"),
        "goals": MessageLookupByLibrary.simpleMessage("اہداف"),
        "income": MessageLookupByLibrary.simpleMessage("آمدنی"),
        "noTransactionsYet": MessageLookupByLibrary.simpleMessage("ابھی تک کوئی لین دین نہیں"),
        "quickActions": MessageLookupByLibrary.simpleMessage("فوری اعمال"),
        "recentTransactions": MessageLookupByLibrary.simpleMessage("حالیہ لین دین"),
        "save": MessageLookupByLibrary.simpleMessage("محفوظ کریں"),
        "setBudget": MessageLookupByLibrary.simpleMessage("بجٹ مقرر کریں"),
        "settings": MessageLookupByLibrary.simpleMessage("ترتیبات"),
        "startByAddingTransaction": MessageLookupByLibrary.simpleMessage("اپنا پہلا لین دین شامل کر کے شروع کریں"),
        "totalBalance": MessageLookupByLibrary.simpleMessage("کل بیلنس"),
        "transactions": MessageLookupByLibrary.simpleMessage("لین دین"),
        "transfer": MessageLookupByLibrary.simpleMessage("منتقلی"),
        "viewAll": MessageLookupByLibrary.simpleMessage("سب دیکھیں"),
      };
}
